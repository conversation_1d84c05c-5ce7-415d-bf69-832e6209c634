/**
 * 节点连接功能样式 - node-connection.css
 *
 * 本文件包含便签节点连接功能的所有样式，包括：
 * 1. 便签选中状态样式
 * 2. 节点按钮样式
 * 3. 底部插槽区域样式
 * 4. 连接线样式
 */

/* 便签选中状态 */
.note.selected {
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.5), 0 0 8px rgba(0, 122, 255, 0.3);
  transition: box-shadow 0.2s ease;
}

/* 便签高亮状态 */
.note.note-highlight {
  box-shadow: 0 0 0 1px rgba(249, 132, 121, 0.4),
    0 0 6px rgba(249, 132, 121, 0.25);
  transition: box-shadow 0.3s ease;
}

/* 节点按钮 - 更像节点软件的连接点 */
.note-node-button {
  position: absolute;
  bottom: 10px;
  left: 10px; /* 放在便签左下角 */
  width: 20px; /* 统一大小 */
  height: 20px; /* 统一大小 */
  border-radius: 50%; /* 圆形按钮 */
  background-color: rgba(66, 133, 244, 0.85);
  border: 2px solid rgba(255, 255, 255, 0.9);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  z-index: 10;
  display: none; /* 默认隐藏，选中时显示 */
  transition: all 0.2s ease;
}

.note-node-button:hover {
  transform: scale(1.1);
  background-color: rgba(92, 154, 255, 0.95);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.25);
}

.note-node-button:active {
  transform: scale(0.9);
  background-color: rgba(66, 133, 244, 1);
}

/* 移除内部箭头图标 */
.note-node-button::before {
  content: none;
}

/* 选中的便签显示节点按钮 */
.note.selected .note-node-button {
  display: block;
}

/* 已连接状态的节点按钮 - 始终显示且样式不同 */
.note-node-button.connected {
  display: block !important; /* 使用!important确保优先级，始终显示 */
  background-color: rgba(52, 168, 83, 0.9); /* 增加不透明度 */
  border-color: rgba(255, 255, 255, 0.95); /* 增加边框不透明度 */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* 增强阴影 */
}

.note-node-button.connected:hover {
  background-color: rgba(52, 168, 83, 0.95);
  /* 移除transform: scale(1.1); 避免文本模糊 */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.25);
}

/* 移除连接点的内部图标 */
.note-node-button.connected::before {
  content: none;
}

/* 底部插槽区域 - 位于底部控制栏上方 */
.slots-container {
  position: fixed;
  bottom: 110px; /* 位于底部控制栏上方，确保不会被遮挡 */
  left: 50%;
  transform: translateX(-50%); /* 水平居中 */
  width: 500px; /* 与底部控制栏宽度一致 */
  max-width: 85%; /* 与底部控制栏最大宽度一致 */
  height: 52px; /* 提供足够空间 */
  background-color: rgba(255, 255, 255, 0.98); /* 更不透明的背景 */
  border: 1px solid rgba(0, 0, 0, 0.08); /* 更精细的边框 */
  border-radius: 14px; /* 略微增加圆角 */
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: flex-start; /* 左对齐 */
  padding: 0 14px;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.03); /* 更精致的阴影 */
  z-index: 9998; /* 确保在底部控制栏下方，但在其他元素上方 */
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1); /* 平滑过渡 */
  overflow-x: auto;
  scrollbar-width: thin;
  opacity: 0; /* 默认隐藏 */
  visibility: hidden; /* 默认隐藏 */
  pointer-events: none; /* 默认不接收事件 */
  /* 移除backdrop-filter: blur(5px); 避免影响文本渲染 */
  /* 移除-webkit-backdrop-filter: blur(5px); 避免影响文本渲染 */
}

/* 自定义滚动条 */
.slots-container::-webkit-scrollbar {
  height: 3px;
}

.slots-container::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.slots-container::-webkit-scrollbar-track {
  background: transparent;
}

/* 显示插槽区域 */
.slots-container.visible {
  opacity: 1; /* 显示 */
  visibility: visible; /* 显示 */
  pointer-events: auto; /* 接收事件 */
  transform: translateX(-50%) translateY(0); /* 无位移动画 */
}

/* 连接插槽标题 */
.slots-title {
  font-size: 12px;
  color: #555;
  margin-right: 14px;
  white-space: nowrap;
  flex-shrink: 0;
  font-weight: 600; /* 稍微加粗 */
  margin-top: 0; /* 移除顶部边距 */
  letter-spacing: 0.2px; /* 字母间距 */
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8); /* 微妙的文字阴影 */
}

/* 插槽容器内的列表区域 */
.slots-list {
  display: flex;
  align-items: center; /* 改为垂直居中 */
  flex-grow: 1;
  overflow-x: auto;
  padding: 0 8px;
  scrollbar-width: none; /* 隐藏滚动条 */
  height: 100%; /* 填充整个容器高度 */
  padding-top: 0; /* 移除顶部内边距 */
  gap: 10px; /* 插槽之间的间距 */
}

.slots-list::-webkit-scrollbar {
  display: none; /* 隐藏滚动条 */
}

/* 清除所有连接的按钮 */
.clear-all-connections {
  padding: 3px 10px;
  background-color: #f8f8f8;
  color: #666;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  margin-left: auto; /* 推到右侧 */
  flex-shrink: 0;
  display: flex;
  align-items: center;
  height: 24px;
  transition: all 0.25s ease;
  margin-top: 0; /* 移除顶部边距 */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); /* 微妙的阴影 */
}

.clear-all-connections:hover {
  background-color: #f1f1f1;
  color: #ea4335;
  border-color: rgba(234, 67, 53, 0.5);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  /* 移除transform: translateY(-1px); 避免文本模糊 */
}

.clear-all-connections:active {
  /* 移除transform: translateY(0); 避免文本模糊 */
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}

.clear-all-connections::before {
  content: "×";
  font-size: 14px;
  margin-right: 4px;
  font-weight: bold;
}

/* 插槽样式 - 优化为更现代的设计 */
.note-slot {
  width: 20px; /* 统一大小 */
  height: 20px; /* 统一大小 */
  margin: 0; /* 使用gap属性控制间距 */
  background-color: rgba(66, 133, 244, 0.85); /* 蓝色 */
  border: 2px solid white;
  border-radius: 50%; /* 圆形 */
  position: relative;
  flex-shrink: 0;
  transition: all 0.2s cubic-bezier(0.25, 0.1, 0.25, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(0, 0, 0, 0.03);
  cursor: pointer;
  z-index: 1; /* 确保插槽在容器内部的层级 */
  margin-top: 0; /* 移除顶部边距 */
}

/* 已连接的插槽 */
.note-slot.connected {
  background-color: rgba(52, 168, 83, 0.9); /* 绿色，增加不透明度 */
  border: 2px solid rgba(255, 255, 255, 0.95);
}

/* 插槽移除按钮 - 更精致的设计 */
.slot-remove {
  position: absolute;
  right: -4px;
  top: -6px; /* 保持在上方 */
  width: 14px; /* 增加大小 */
  height: 14px; /* 增加大小 */
  border-radius: 50%;
  background-color: rgba(234, 67, 53, 0.95); /* 红色背景，更醒目 */
  border: 1px solid white;
  color: white;
  font-size: 10px; /* 字体大小 */
  display: none; /* 默认隐藏 */
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.25, 0.1, 0.25, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15); /* 增强阴影 */
  z-index: 10; /* 增加z-index确保显示在最上层 */
  /* 移除transform: scale(0.9); 避免文本模糊 */
}

/* 悬停时显示移除按钮 */
.note-slot:hover .slot-remove {
  display: flex;
  /* 移除transform: scale(1); 避免文本模糊 */
}

.slot-remove:hover {
  background-color: rgba(211, 47, 47, 1);
  /* 移除transform: scale(1.15); 避免文本模糊 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slot-remove:active {
  /* 移除transform: scale(0.95); 避免文本模糊 */
}

/* 添加序号标记在插槽上 */
.note-slot::after {
  content: attr(data-index);
  position: absolute;
  top: 45%; /* 使用固定位置而不是transform */
  left: 45%; /* 使用固定位置而不是transform */
  /* 移除transform: translate(-50%, -50%); 避免文本模糊 */
  font-size: 10px; /* 稍大字体 */
  color: white;
  font-weight: bold;
  /* 移除text-shadow避免文本模糊 */
}

/* 插槽悬停效果 */
.note-slot:hover {
  /* 移除transform: scale(1.1); 避免文本模糊 */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
}

/* 插槽高亮状态 */
.note-slot.slot-highlight {
  /* 移除transform: scale(1.08); 避免文本模糊 */
  box-shadow: 0 2px 6px rgba(249, 132, 121, 0.3),
    0 0 0 1px rgba(249, 132, 121, 0.2);
  border-color: rgba(255, 255, 255, 1);
  background-color: rgba(249, 132, 121, 0.8);
}

/* 连接线容器 - 已由Leader-Line库接管，仅保留占位区域 */
.connections-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10; /* 确保在插槽容器上方但不要太高，避免影响其他UI交互 */
}

/* 使用CSS变量定义配色 */
:root {
  --note-connection-color: rgba(110, 150, 190, 0.55); /* 淡雅蓝色，半透明 */
  --note-connection-highlight-color: rgba(
    249,
    132,
    121,
    0.65
  ); /* 柔和的粉红色 */
  --note-connection-size: 1.5px; /* 更细的连接线 */
  --note-connection-highlight-size: 2px; /* 稍微粗一些的高亮线 */
  --note-to-note-connection-color: rgba(
    100,
    200,
    100,
    0.6
  ); /* 便签到便签的连接线颜色 */
}

/* Leader-Line特定样式 - 控制SVG渲染层 */
.leader-line {
  z-index: 9990 !important; /* 确保比底部栏低但比普通元素高 */
}

/* leader-line 容器样式 */
.connection-line-container {
  pointer-events: auto !important;
}

/* 增强Leader-Line的视觉效果 */
.leader-line path {
  /* 移除filter: drop-shadow(); 避免影响文本渲染 */
  transition: stroke-width 0.3s ease, opacity 0.3s ease;
  opacity: 0.85; /* 稍微透明 */
}

/* 高亮状态的连接线 */
.leader-line.highlight path {
  stroke-width: var(--note-connection-highlight-size) !important;
  /* 移除filter: drop-shadow(); 避免影响文本渲染 */
  opacity: 1 !important;
}

/* 使连接线在便签下方但在背景上方 */
:root {
  --note-connection-color: #4682b4;
  --note-connection-highlight-color: #ff6b6b;
}

/* 连接模式下的底部栏样式 */
.bottom-bar.connection-mode {
  border: 2px solid rgba(52, 168, 83, 0.6); /* 加粗边框并增加不透明度 */
  box-shadow: 0 2px 10px rgba(52, 168, 83, 0.2),
    0 0 0 1px rgba(52, 168, 83, 0.15); /* 增强阴影效果 */
  background-color: rgba(255, 255, 255, 0.98); /* 略微调整背景 */
  animation: connection-mode-pulse 2s infinite alternate; /* 添加轻微脉动效果 */
}

/* 连接模式下的输入框样式 */
.ai-prompt.connection-input-mode {
  background-color: rgba(240, 255, 240, 0.8); /* 淡绿色背景，增加不透明度 */
  border: 2px solid rgba(52, 168, 83, 0.5); /* 加粗边框并增加不透明度 */
  color: #015d01; /* 深绿色文字 */
  font-weight: 500; /* 稍微加粗 */
}

.ai-prompt.connection-input-mode::placeholder {
  color: rgba(52, 168, 83, 0.7); /* 绿色占位符文本 */
  font-weight: 500; /* 稍微加粗 */
}

/* 已连接模式下的AI生成按钮样式增强 */
#ai-generate.connected-mode {
  background-color: #34a853; /* 绿色背景 */
  border-color: rgba(52, 168, 83, 0.9); /* 增加边框不透明度 */
  box-shadow: 0 2px 8px rgba(52, 168, 83, 0.4); /* 增强绿色阴影 */
  /* 移除transform: scale(1.05); 避免文本模糊 */
  transition: all 0.3s ease;
}

#ai-generate.connected-mode:hover {
  background-color: #2d9649; /* 悬停时深绿色 */
  box-shadow: 0 3px 10px rgba(52, 168, 83, 0.5); /* 更明显的阴影 */
  /* 移除transform: scale(1.1); 避免文本模糊 */
}

/* 连接模式的脉动动画 */
@keyframes connection-mode-pulse {
  from {
    box-shadow: 0 2px 10px rgba(52, 168, 83, 0.2),
      0 0 0 1px rgba(52, 168, 83, 0.15);
    border-color: rgba(52, 168, 83, 0.6);
  }
  to {
    box-shadow: 0 2px 12px rgba(52, 168, 83, 0.3),
      0 0 0 1px rgba(52, 168, 83, 0.2);
    border-color: rgba(52, 168, 83, 0.8);
  }
}
