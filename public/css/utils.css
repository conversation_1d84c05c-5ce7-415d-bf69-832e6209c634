/**
 * 工具类样式表 - utils.css
 *
 * 本文件定义了整个应用中使用的辅助工具类样式，包括：
 * 1. 消息提示：如错误消息、成功提示等通知样式
 * 2. 辅助类：可能包含的定位、显示、间距等通用辅助类
 * 3. 动画效果：消息显示和隐藏的过渡动画
 *
 * 这些工具类样式提供了应用中常用的UI效果和功能，
 * 可以在不同组件中复用，保持视觉一致性和代码简洁。
 */

/* 工具类和辅助样式 */

/* 错误消息样式 */
.error-message {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%) translateY(20px);
  background-color: #ff3333;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.3s, transform 0.3s;
  z-index: 1000;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  text-align: center;
  max-width: 80%;
}

.error-message.show {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

/* 消息提示样式 */
.message {
  position: fixed;
  bottom: 100px; /* 使用固定位置而不是transform */
  left: 50%;
  margin-left: -40%; /* 使用margin居中而不是transform */
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.3s, bottom 0.3s; /* 改为bottom动画 */
  z-index: 1000;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  text-align: center;
  max-width: 80%;
}

.message.show {
  opacity: 1;
  bottom: 80px; /* 显示时移动到最终位置 */
}

.message-info {
  background-color: #e3f2fd;
  color: #0d47a1;
}

.message-success {
  background-color: #e8f5e9;
  color: #1b5e20;
}

.message-error {
  background-color: #ffebee;
  color: #b71c1c;
}

.message-warning {
  background-color: #fff8e1;
  color: #ff6f00;
}

/* 连接状态样式 */
#connection-status {
  margin-left: 10px;
  font-size: 14px;
}

.status-success {
  color: #2e7d32;
}

.status-error {
  color: #c62828;
}

/* 圆形按钮通用类 - 确保任何按钮应用这个类都会是完美圆形 */
.circle-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  padding: 0;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  box-sizing: border-box;
  font-size: inherit;
  background-color: transparent;
  color: inherit;
  cursor: pointer;
}

/* 圆形按钮尺寸变体 */
.circle-button-sm {
  width: 28px;
  height: 28px;
  font-size: 14px;
}

.circle-button-lg {
  width: 40px;
  height: 40px;
  font-size: 20px;
}
