/* 减少内容区下方空白 */
.ai-content-area {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding-bottom: 0;
}

.ai-content-wrapper {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 10px;
}

/* 美化滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 这些设置相关的样式已移至settings-unified-new.css */

/* Coming Soon相关样式已移至settings-unified-new.css */
.icon-paintbrush:before {
  content: "🎨";
}

/* 统一的设置状态提示区域样式 */
.settings-status-area {
  margin-bottom: 15px;
  padding: 0;
  min-height: 0;
  transition: min-height 0.3s ease;
  overflow: hidden;
  border-radius: 6px;
  background-color: #f8f9fa;
  border: 1px solid #eaeaea;
}

.settings-status-area.has-message {
  min-height: 44px;
  margin-bottom: 15px;
}

.settings-status-message {
  padding: 10px 12px;
  font-size: 14px;
  margin: 0;
  display: flex;
  align-items: center;
  border-radius: 4px;
}

.settings-status-message:before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

/* 成功状态 */
.settings-status-message.status-success {
  background-color: #e8f5e9;
  color: #1b5e20;
}

.settings-status-message.status-success:before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%231b5e20' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M22 11.08V12a10 10 0 1 1-5.93-9.14'%3E%3C/path%3E%3Cpolyline points='22 4 12 14.01 9 11.01'%3E%3C/polyline%3E%3C/svg%3E");
}

/* 错误状态 */
.settings-status-message.status-error {
  background-color: #ffebee;
  color: #b71c1c;
}

.settings-status-message.status-error:before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23b71c1c' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='12' cy='12' r='10'%3E%3C/circle%3E%3Cline x1='12' y1='8' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='16' x2='12.01' y2='16'%3E%3C/line%3E%3C/svg%3E");
}

/* 信息状态 */
.settings-status-message.status-info {
  background-color: #e3f2fd;
  color: #0d47a1;
}

.settings-status-message.status-info:before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%230d47a1' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3C/circle%3E%3C/circle%3E%3Cline x1='12' y1='16' x2='12' y2='12'%3E%3C/line%3E%3Cline x1='12' y1='8' x2='12.01' y2='8'%3E%3C/line%3E%3C/svg%3E");
}

/* 加载中状态 */
.settings-status-message.status-loading {
  background-color: #fffde7;
  color: #ff6f00;
}

.settings-status-message.status-loading:before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23ff6f00' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='12' y1='2' x2='12' y2='6'%3E%3C/line%3E%3Cline x1='12' y1='18' x2='12' y2='22'%3E%3C/line%3E%3Cline x1='4.93' y1='4.93' x2='7.76' y2='7.76'%3E%3C/line%3E%3Cline x1='16.24' y1='16.24' x2='19.07' y2='19.07'%3E%3C/line%3E%3Cline x1='2' y1='12' x2='6' y2='12'%3E%3C/line%3E%3Cline x1='18' y1='12' x2='22' y2='12'%3E%3C/line%3E%3Cline x1='4.93' y1='19.07' x2='7.76' y2='16.24'%3E%3C/line%3E%3Cline x1='16.24' y1='7.76' x2='19.07' y2='4.93'%3E%3C/line%3E");
  animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 备份和恢复操作按钮样式 */
.export-button,
.file-input-label {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background-color: #f5f5f7;
  border: 1px solid #d2d2d7;
  border-radius: 6px;
  color: #1d1d1f;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.export-button:hover,
.file-input-label:hover {
  background-color: #e8e8ed;
  border-color: #c4c4c9;
}

.export-button:active,
.file-input-label:active {
  background-color: #dcdce0;
  transform: scale(0.98);
}

/* 导出按钮前添加图标 */
.export-button::before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%231d1d1f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4'%3E%3C/path%3E%3Cpolyline points='7 10 12 15 17 10'%3E%3C/polyline%3E%3Cline x1='12' y1='15' x2='12' y2='3'%3E%3C/line%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

/* 导入按钮前添加图标 */
.file-input-label::before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%231d1d1f' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4'%3E%3C/path%3E%3Cpolyline points='17 8 12 3 7 8'%3E%3C/polyline%3E%3Cline x1='12' y1='3' x2='12' y2='15'%3E%3C/line%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

/* 隐藏原始文件输入 */
.file-input {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 导入导出操作的提示样式 */
.backup-status {
  margin-top: 8px;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 13px;
  display: none;
}

.backup-status.show {
  display: block;
}

.backup-status.success {
  background-color: #e8f5e9;
  color: #1b5e20;
}

.backup-status.error {
  background-color: #ffebee;
  color: #b71c1c;
}

.backup-status.info {
  background-color: #e3f2fd;
  color: #0d47a1;
}

/* 重置按钮样式 */
.reset-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  background-color: #f5f5f7;
  border: 1px solid #d2d2d7;
  border-radius: 6px;
  color: #1d1d1f;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  margin-top: 20px;
}

/* 重置按钮警告状态 */
.reset-button.reset-warning {
  background-color: #fff3e0;
  border-color: #ffcc80;
  color: #e65100;
}

/* 重置按钮危险状态 */
.reset-button.reset-danger {
  background-color: #ffebee;
  border-color: #ef9a9a;
  color: #b71c1c;
}

/* 重置进度指示器 */
.reset-progress {
  margin-left: 8px;
  font-size: 12px;
  letter-spacing: 2px;
}

/* --- Top Center Notification Styles --- */
.message-top-center {
  position: fixed; /* 固定定位，相对于视口 */
  top: 20px; /* 距离顶部 20px */
  left: 50%; /* 水平居中 */
  transform: translateX(-50%) translateY(-100px); /* 初始位置在屏幕上方外，并水平居中 */
  padding: 12px 25px;
  border-radius: 8px;
  background-color: rgba(50, 50, 50, 0.9); /* 深灰色背景，带透明度 */
  color: #fff; /* 白色文字 */
  font-size: 14px;
  font-weight: 500;
  z-index: 10000; /* 确保在最上层 */
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1),
    opacity 0.4s ease-out; /* 平滑过渡效果 */
  pointer-events: none; /* 避免遮挡下方元素 */
  max-width: 80%; /* 最大宽度，防止过长 */
  text-align: center;
}

.message-top-center.show {
  transform: translateX(-50%) translateY(0); /* 动画结束状态：移动到目标位置 */
  opacity: 1;
}

/* 不同类型的通知颜色 */
.message-top-center.message-success {
  background-color: rgba(76, 175, 80, 0.9); /* 绿色 */
}

.message-top-center.message-error {
  background-color: rgba(244, 67, 54, 0.9); /* 红色 */
}

.message-top-center.message-warning {
  background-color: rgba(255, 152, 0, 0.9); /* 橙色 */
}

.message-top-center.message-info {
  background-color: rgba(33, 150, 243, 0.9); /* 蓝色 */
}

/* 移除旧的底部消息样式 (如果存在) */
/* .message { ... } */
/* .message.show { ... } */
/* .message-success { ... } */
/* etc. */

/* AI生成便签的高亮效果 - 苹果风格阴影 */
.new-note-highlight {
  animation: apple-style-glow 2s cubic-bezier(0.23, 1, 0.32, 1);
}

/* 苹果风格的柔和阴影效果 - 从生成中的效果平滑过渡 */
@keyframes apple-style-glow {
  0% {
    /* 与生成中的效果保持一致，确保平滑过渡 */
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.3),
      0 0 15px rgba(0, 122, 255, 0.4), 0 0 30px rgba(0, 122, 255, 0.2);
    /* 移除transform: scale(1.005); 避免文本模糊 */
  }
  30% {
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2),
      0 0 10px rgba(0, 122, 255, 0.3), 0 0 20px rgba(0, 122, 255, 0.1);
    /* 移除transform: scale(1.003); 避免文本模糊 */
  }
  60% {
    box-shadow: 0 0 0 1px rgba(0, 122, 255, 0.15),
      0 0 6px rgba(0, 122, 255, 0.2), 0 0 12px rgba(0, 122, 255, 0.05);
    /* 移除transform: scale(1.001); 避免文本模糊 */
  }
  100% {
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.04), 0 2px 4px rgba(0, 0, 0, 0.12); /* 恢复到普通便签的阴影 */
    /* 移除transform: scale(1); 避免文本模糊 */
  }
}

/* 用户状态和登出按钮样式 */
.user-status {
  display: flex;
  align-items: center;
  margin-left: auto;
  margin-right: 20px;
  font-size: 12px;
  color: #666;
}

.user-label {
  margin-right: 5px;
  opacity: 0.7;
}

.user-display {
  font-weight: 500;
}

.logout-button {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  margin-left: 8px;
  padding: 2px 5px;
  border-radius: 3px;
  transition: all 0.2s;
}

.logout-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
}

.icon-logout {
  font-style: normal;
  display: inline-block;
  transform: rotate(180deg);
}

/* 用户管理面板样式 */
.user-management-container {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.user-info-section,
.password-change-section {
  background-color: rgba(250, 250, 250, 0.5);
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(0, 0, 0, 0.04);
}

.user-info-box {
  display: flex;
  align-items: flex-start;
  margin-top: 15px;
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.user-info-box:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  /* 移除transform: translateY(-2px); 避免文本模糊 */
}

.user-avatar {
  width: 70px;
  height: 70px;
  background-color: #e6eef8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 25px;
  font-size: 32px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
  color: #4a90e2;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 22px;
  font-weight: 500;
  margin: 0 0 6px 0;
  color: #333;
}

.user-role {
  font-size: 14px;
  color: #666;
  margin: 0 0 15px 0;
  background-color: rgba(74, 144, 226, 0.1);
  padding: 3px 10px;
  border-radius: 12px;
  display: inline-block;
}

.user-actions {
  margin-top: 15px;
}

/* 登出按钮改为更明显的设计 */
#settings-logout-button {
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 8px 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

#settings-logout-button:hover {
  background-color: #ffebee;
  color: #e53935;
  border-color: #ffcdd2;
}

#settings-logout-button .icon-logout {
  font-style: normal;
  display: inline-block;
  transform: rotate(180deg);
}

/* 密码修改区域优化 */
.password-change-section {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  margin-top: 30px;
  border: 1px solid #eee;
}

.password-change-section h4 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.password-actions {
  margin-top: 25px;
  display: flex;
  justify-content: flex-end;
}

/* 可以删除底部状态栏中的用户状态相关样式，因为已经不再使用 */
/* 或者保留它们以便将来可能的重用 */
#change-password-button {
  /* 使用与生成新邀请码按钮相同的样式 */
  padding: 8px 16px;
  background-color: #1a73e8;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

#change-password-button:hover {
  background-color: #1765cc;
}

#change-password-button:disabled {
  background-color: #a0c4e8;
  cursor: not-allowed;
}

/* 个人中心相关样式已移至settings-unified-new.css */

/* 邀请码管理样式 */
.invite-code-container {
  padding: 24px;
}

.invite-actions {
  margin-bottom: 24px;
}

.icon-plus {
  font-style: normal;
  margin-right: 4px;
}

.invite-codes-list h5 {
  font-size: 16px;
  color: #495057;
  margin: 0 0 16px 0;
  font-weight: 500;
}

.empty-state {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  text-align: center;
  color: #6c757d;
}

#invite-codes-container {
  list-style: none;
  padding: 0;
  margin: 0;
}

.invite-code-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 10px;
  border: 1px solid #e9ecef;
}

.invite-code {
  flex: 1;
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 14px;
  color: #495057;
  letter-spacing: 1px;
  background-color: #e9ecef;
  padding: 4px 10px;
  border-radius: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.invite-code-actions {
  display: flex;
  gap: 8px;
  margin-left: 12px;
}

.invite-code-button {
  background: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
  border-radius: 4px;
  padding: 4px 8px;
  transition: all 0.2s;
}

.copy-button {
  color: #1971c2;
}

.copy-button:hover {
  background-color: #e7f5ff;
}

.delete-button {
  color: #e03131;
}

.delete-button:hover {
  background-color: #fff5f5;
}

.copied-feedback {
  display: inline-block;
  font-size: 12px;
  color: #2b8a3e;
  margin-left: 8px;
  opacity: 0;
  transition: opacity 0.3s;
}

.copied-feedback.show {
  opacity: 1;
}

/* API不可用消息样式 */
.api-unavailable-message {
  padding: 20px;
  background-color: #fff5f5;
  border-radius: 8px;
  text-align: center;
  color: #e03131;
  border: 1px dashed #ffc9c9;
  margin: 15px 0;
}

.api-unavailable-message p {
  margin: 5px 0;
}

/* 禁用的按钮样式 */
.primary-button:disabled {
  background-color: #adb5bd;
  cursor: not-allowed;
  opacity: 0.7;
}

/* 历史记录功能样式调整 */
.history-input-container {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.history-dropdown {
  position: absolute;
  top: calc(100% + 5px);
  right: 0;
  width: 100%;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
  max-height: 200px;
  overflow-y: auto;
  display: none;
}

.history-dropdown.active {
  display: block;
}

.history-dropdown-content {
  padding: 0;
}

.history-item {
  padding: 10px 14px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-item:hover {
  background-color: #f0f4f8;
}

.history-item-value {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.history-item-info {
  color: #999;
  font-size: 12px;
  margin-left: 8px;
}

.history-item-actions {
  display: flex;
  align-items: center;
}

.history-item-delete {
  background: none;
  border: none;
  color: #d32f2f;
  cursor: pointer;
  padding: 4px;
  font-size: 16px;
  opacity: 0.7;
  transition: opacity 0.2s;
  margin-left: 5px;
}

.history-item-delete:hover {
  opacity: 1;
}

.history-item-loading {
  padding: 15px;
  text-align: center;
  color: #666;
}

.history-empty {
  padding: 15px;
  text-align: center;
  color: #888;
  font-style: italic;
}

/* 输入框样式修改 */
#ai-api-key {
  padding-right: 40px; /* 为显示/隐藏按钮留出空间 */
}

.toggle-visibility {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  transition: background-color 0.2s;
  z-index: 3;
}
