/**
 * 主样式文件 - main.css
 *
 * 本文件是应用的CSS入口点，通过导入所有模块化的样式文件实现样式组织，包括：
 * 1. 基础样式：重置规则和基本页面布局
 * 2. 组件样式：各个功能组件的专用样式
 * 3. 工具类：通用辅助样式和功能类
 *
 * 采用模块化的CSS导入方式有助于代码分离和维护，
 * 每个功能区域的样式都被组织在独立的文件中，便于开发和调试。
 */

/* 主样式文件，引入所有模块 */

/* 引入基础样式 */
@import url("base.css");

/* 引入组件样式 */
@import url("bottom-bar.css");
/* @import url("drawer.css"); */ /* 已删除网页抽屉功能 */
/* @import url("tabs.css"); */ /* 已删除网页抽屉标签页功能 */
@import url("canvas.css");
@import url("notes.css");
@import url("markdown.css");
@import url("settings-unified.css"); /* 统一设置页面样式 */
@import url("node-connection.css"); /* 便签节点连接功能样式 */
@import url("global-tooltip.css"); /* 全局tooltip样式 */

/* 引入工具类 */
@import url("utils.css");

/* AI 生成控制容器样式 */
.ai-generation-controls {
  display: flex;
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
  align-items: center;
  justify-content: space-between;
  height: 30px;
  z-index: 10;
}

/* 左侧控制容器样式 */
.generation-left-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* AI 打字效果的指示器样式 */
.ai-typing-indicator {
  display: flex;
  align-items: center;
  height: 20px;
}

/* 生成进度指示器样式 */
.generation-progress {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  background-color: rgba(240, 248, 255, 0.85);
  padding: 3px 8px;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  display: inline-flex;
  align-items: center;
  border: 1px solid rgba(200, 230, 255, 0.7);
}

.generation-progress .chars-count {
  font-weight: 500;
  color: #1a73e8;
  margin: 0 2px;
}

/* 取消生成按钮样式已移除，现在使用 AI 生成按钮的 generating 状态 */

/* 现代化的打字指示器样式 */
.ai-typing-indicator span {
  display: inline-block;
  width: 6px;
  height: 6px;
  margin-right: 4px;
  background: linear-gradient(135deg, #4a90e2, #7c4dff);
  border-radius: 50%;
  opacity: 0.7;
  animation: typing-dot 1.4s infinite ease-in-out both;
  box-shadow: 0 0 4px rgba(74, 144, 226, 0.3);
}

.ai-typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.ai-typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.ai-typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing-dot {
  0%,
  80%,
  100% {
    transform: scale(0.7) translateY(0);
    opacity: 0.4;
  }
  40% {
    transform: scale(1) translateY(-3px);
    opacity: 1;
    box-shadow: 0 0 8px rgba(74, 144, 226, 0.5);
  }
}

/* AI便签生成中的蓝色阴影效果 */
.note:has(.ai-generation-controls),
.ai-generating-note {
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2), 0 0 8px rgba(0, 122, 255, 0.3),
    0 0 16px rgba(0, 122, 255, 0.1);
  transition: box-shadow 0.3s ease, transform 0.3s ease;
  animation: ai-generating-glow 3s infinite ease-in-out;
  transform-origin: center center;
}

/* 添加微妙的缩放呼吸效果 */
.note:has(.ai-generation-controls),
.ai-generating-note {
  animation: ai-generating-glow 3s infinite ease-in-out,
    ai-generating-pulse 3s infinite ease-in-out;
}

/* AI便签生成中的呼吸光效果 - 更自然的呼吸节奏 */
@keyframes ai-generating-glow {
  0% {
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.15),
      0 0 8px rgba(0, 122, 255, 0.25), 0 0 16px rgba(0, 122, 255, 0.08);
  }
  50% {
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.3),
      0 0 15px rgba(0, 122, 255, 0.4), 0 0 30px rgba(0, 122, 255, 0.2);
  }
  100% {
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.15),
      0 0 8px rgba(0, 122, 255, 0.25), 0 0 16px rgba(0, 122, 255, 0.08);
  }
}

/* 微妙的缩放呼吸动画 */
@keyframes ai-generating-pulse {
  0% {
    /* 移除transform: scale(1); 避免文本模糊 */
  }
  50% {
    /* 移除transform: scale(1.005); 避免文本模糊 */
  }
  100% {
    /* 移除transform: scale(1); 避免文本模糊 */
  }
}

/* 让markdown内容区域支持滚动 */
.note-content.markdown {
  overflow-y: auto;
  max-height: calc(100% - 30px); /* 减去标题高度 */
  padding: 12px 14px; /* 上下12px，左右14px */
}

.note-content.markdown pre {
  white-space: pre-wrap;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 6px;
}

/* 改进 Markdown 预览区域样式 */
.markdown-preview {
  overflow-y: auto;
  max-height: calc(100% - 30px); /* 减去标题高度 */
  padding: 12px 14px; /* 上下12px，左右14px */
  line-height: 1.5;
  white-space: normal; /* 确保正常换行 */
}

.markdown-preview p {
  margin-bottom: 0.8em;
}

.markdown-preview h1,
.markdown-preview h2,
.markdown-preview h3,
.markdown-preview h4,
.markdown-preview h5,
.markdown-preview h6 {
  margin-top: 1em;
  margin-bottom: 0.5em;
  font-weight: bold;
}

.markdown-preview ul,
.markdown-preview ol {
  padding-left: 1.5em;
  margin-bottom: 0.8em;
}

.markdown-preview pre {
  margin-bottom: 1em;
}

/* 临时生成便签的预览样式 */
.note .ai-typing-indicator + .note-body .markdown-preview {
  min-height: 50px; /* 确保在生成时有最小高度 */
}

/* 移除旧的AI设置样式，已迁移到settings-unified-new.css */

/* 温度值显示样式 - 显示在调节条右侧 */
#temperature-value {
  display: inline-block;
  margin-left: 10px;
  color: #666;
  font-size: 12px;
  transition: color 0.1s ease;
  min-width: 24px;
  text-align: center;
}

#temperature-value.updating {
  color: #0071e3;
}

/* 模型输入框样式增强 */
#ai-model {
  width: 100%;
}

/* datalist下拉提示样式增强 */
input[list]::-webkit-calendar-picker-indicator {
  opacity: 0.5;
}

input[list]:focus::-webkit-calendar-picker-indicator {
  opacity: 0.8;
}

/* 自定义选择框容器样式 */
.custom-select-container {
  position: relative;
  width: 100%;
}

.custom-select-container input[type="text"] {
  padding-right: 30px; /* 为下拉箭头腾出空间 */
  cursor: pointer;
}

.select-toggle {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #666;
  font-size: 10px;
  z-index: 1;
}

.select-toggle:hover {
  color: #333;
}

.select-arrow {
  transition: transform 0.2s;
}

.custom-select-container.open .select-arrow {
  transform: rotate(180deg);
}

.custom-select-dropdown {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  width: 100%;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 100;
  max-height: 220px;
  overflow-y: auto;
  opacity: 0;
  transform: translateY(-10px);
  visibility: hidden;
  transition: opacity 0.2s, transform 0.2s, visibility 0.2s;
}

.custom-select-container.open .custom-select-dropdown {
  opacity: 1;
  transform: translateY(0);
  visibility: visible;
}

.select-option {
  padding: 10px 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.select-option:hover {
  background-color: #f5f5f5;
}

.select-option.selected {
  background-color: #e3f2fd;
  color: #1a73e8;
  font-weight: 500;
}

/* 自定义滚动条样式 */
.custom-select-dropdown::-webkit-scrollbar {
  width: 6px;
}

.custom-select-dropdown::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.custom-select-dropdown::-webkit-scrollbar-track {
  background: transparent;
}

/* 便签合集弹窗样式 - 修改为抽屉模式 */
.reading-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10001;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.reading-modal.visible {
  display: flex;
  opacity: 1;
  justify-content: center;
  align-items: flex-end; /* 改为底部对齐，从下往上弹出 */
}

.reading-container {
  background-color: #fff;
  width: 98%; /* 调整为屏幕宽度的98% */
  height: 95%; /* 调整为屏幕高度的95% */
  border-radius: 12px 12px 0 0; /* 只保留顶部圆角 */
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transform: translateY(100%); /* 初始位置在屏幕底部外 */
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* 添加平滑过渡效果 */
  /* 确保容器内容正确布局 */
  max-height: 95vh;
}

.reading-modal.visible .reading-container {
  transform: translateY(0); /* 显示时滑入屏幕 */
}

/* 修改便签合集头部样式，与设置页面保持一致 */
.reading-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px; /* 调整为与设置页面完全相同的内边距 */
  border-bottom: 1px solid #e0e0e0;
  height: 66px; /* 精确设置头部高度，与设置页面一致 */
  box-sizing: border-box; /* 确保padding不会增加总高度 */
  flex-shrink: 0; /* 防止头部被压缩 */
}

.reading-header h2 {
  margin: 0;
  font-size: 20px; /* 调整为与设置页面一致 */
  font-weight: 600; /* 调整为与设置页面一致 */
  color: #333;
}

/* 修改关闭按钮样式，与设置页面保持一致 */
.close-reading {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f5f5f7;
  border: none;
  color: #333;
  font-size: 24px;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  padding: 0; /* 确保没有内边距干扰 */
  margin: 0; /* 确保没有外边距干扰 */
}

.close-reading:hover {
  background-color: #e8e8ed;
}

.close-reading:active {
  background: linear-gradient(to top, #e9ecef, #dee2e6);
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.15);
  transform: translateY(1px);
}

.reading-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧便签列表导航 */
.reading-nav {
  width: 250px;
  border-right: 1px solid #e0e0e0;
  overflow-y: auto;
  padding: 15px 0;
  background-color: #f7f9fc;
}

.reading-nav .nav-item {
  display: block;
  width: 100%;
  padding: 12px 20px;
  text-align: left;
  background: none;
  border: none;
  border-left: 3px solid transparent;
  font-size: 0.95rem;
  color: #555;
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.reading-nav .nav-item:hover {
  background-color: #edf1f7;
  color: #333;
}

.reading-nav .nav-item.active {
  background-color: #e6eef7;
  border-left-color: #4285f4;
  color: #4285f4;
  font-weight: 500;
}

/* 右侧便签内容面板 */
.reading-panels {
  flex: 1;
  overflow-y: auto;
  padding: 20px 20px 70px 20px; /* 增加底部内边距，为功能区域留出空间 */
  position: relative;
}

.reading-panel {
  display: none;
  height: 100%;
  overflow-y: auto;
}

.reading-panel.active {
  display: block;
}

.reading-panel .note-content {
  line-height: 1.6;
  color: #333;
}

/* 无便签时的提示 */
.no-notes-message {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #888;
  text-align: center;
  font-size: 1.1rem;
  line-height: 1.6;
}

.no-notes-message p:first-child {
  margin-bottom: 10px;
  font-weight: 500;
}

/* 便签合集面板内容样式 */
.reading-panel-content {
  line-height: 1.6;
  color: #333;
  font-size: 14px;
  padding: 15px;
  max-width: 900px;
  margin: 0 auto;
}

.reading-panel-content p {
  margin-bottom: 1em;
}

.reading-panel-content h1,
.reading-panel-content h2,
.reading-panel-content h3,
.reading-panel-content h4,
.reading-panel-content h5,
.reading-panel-content h6 {
  margin-top: 1.5em;
  margin-bottom: 0.75em;
  font-weight: 600;
  line-height: 1.25;
}

.reading-panel-content h1 {
  font-size: 2em;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.3em;
}

.reading-panel-content h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.3em;
}

.reading-panel-content h3 {
  font-size: 1.25em;
}

.reading-panel-content h4 {
  font-size: 1em;
}

.reading-panel-content ul,
.reading-panel-content ol {
  padding-left: 2em;
  margin-bottom: 1em;
}

.reading-panel-content ul li,
.reading-panel-content ol li {
  margin-bottom: 0.25em;
}

.reading-panel-content pre {
  background-color: #f6f8fa;
  border-radius: 3px;
  padding: 16px;
  overflow: auto;
  margin-bottom: 1em;
}

.reading-panel-content code {
  font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.9em;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
  padding: 0.2em 0.4em;
}

.reading-panel-content pre code {
  background-color: transparent;
  padding: 0;
}

.reading-panel-content blockquote {
  margin: 0 0 1em;
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
}

.reading-panel-content img {
  max-width: 100%;
  margin: 1em 0;
}

.reading-panel-content table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 1em;
  overflow-x: auto;
  display: block;
}

.reading-panel-content table th,
.reading-panel-content table td {
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

.reading-panel-content table tr {
  background-color: #fff;
  border-top: 1px solid #c6cbd1;
}

.reading-panel-content table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

/* 优化阅读面板的滚动条 */
.reading-panel::-webkit-scrollbar {
  width: 8px;
}

.reading-panel::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.reading-panel::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

.reading-panel::-webkit-scrollbar-thumb:hover {
  background: #aaa;
}

/* 便签合集加载指示器样式 */
.reading-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.reading-loading p {
  margin-top: 15px;
  color: #666;
  font-size: 14px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 113, 227, 0.1);
  border-radius: 50%;
  border-top-color: #0071e3;
  animation: spinner 0.8s linear infinite;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

/* 阅读模式右侧文本区域底部功能区域样式 */
.reading-panel-footer {
  border-top: 1px solid #e0e0e0;
  padding: 12px 20px;
  background-color: #f9f9f9;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 50px;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 5;
}

.reading-panel-actions {
  display: flex;
  gap: 10px;
  width: 100%;
  justify-content: center;
}

.reading-actions-placeholder {
  color: #888;
  font-size: 14px;
  font-style: italic;
  display: flex;
  align-items: center;
  justify-content: center;
}
